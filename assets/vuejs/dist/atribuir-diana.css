.vld-shown {
  overflow: hidden;
}

.vld-overlay {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  align-items: center;
  display: none;
  justify-content: center;
  overflow: hidden;
  z-index: 9999;
}

.vld-overlay.is-active {
  display: flex;
}

.vld-overlay.is-full-page {
  z-index: 9999;
  position: fixed;
}

.vld-overlay .vld-background {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  background: #fff;
  opacity: 0.5;
}

.vld-overlay .vld-icon, .vld-parent {
  position: relative;
}

.align-center {
    display: flex;
    margin-right: 10px;
    align-items: center;
}

.form-check-input:hover {
    cursor: pointer;
}

.padding-none {
    padding: 0px !important;
}

.vertical-middle {
    vertical-align: middle !important;
    text-align: center;
}

#diana-atribuir-grupo {
    max-height: 75vh;
    overflow-y: auto;
}