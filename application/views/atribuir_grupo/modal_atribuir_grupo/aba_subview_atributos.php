<div class="row">
    <?php if (isset($items) && count($items) > 0 && isset($ncm) && !empty($ncm)) : ?>
        <div id="v-cad-item-attr-group-mount">
            <v-cad-item-attr-group
                local="dados_tecnicos"
                :is-editable="false"
                :params="{
                    'id_grupo_tarifario': '<?php echo !empty($idGrupoTarifario) ? $idGrupoTarifario : '' ?>',
                    'ncm': '<?php echo !empty($ncm) ? $ncm : ''; ?>'
                }"
            ></v-cad-item-attr-group>
        </div>
        <script type="text/javascript" src="<?php echo base_url('assets/vuejs/dist/cad-item-attr.js?version='.config_item('assets_version')) ?>"></script>
        <link rel="stylesheet" href="<?php echo base_url('assets/vuejs/dist/cad-item-attr.css?version='.config_item('assets_version')) ?>" type="text/css" media="screen" />
    <?php else: ?>
        <?php if (isset($ncm) && !empty($ncm)) : ?>
            <div class="col-sm-12">
                <p>Não há atributos disponíveis para o NCM informado [<strong><?php echo $ncm ?></strong>].</p>
            </div>
        <?php else : ?>
            <div class="col-sm-12">
                <p>Informe um NCM para escolher o atributo.</p>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<style>
    /* Estabelecer tamanho mínimo de altura da aba pois quando a NCM só existe um atributo, a aba fica muito pequena, e o select do atributo pode ter muitas opções */
    #v-cad-item-attr-group-mount {
        min-height: 500px;
    }
</style>